describe('Quiz Response', () => {
  beforeEach(() => {
    cy.fixture('users').then((user) => {
      cy.login(user.superAdmin.username, user.superAdmin.password);
    });
    cy.enterQuiz();
    cy.searchQuizFormName('ฟอร์มไว้สำหรับ Test Cypress');
  });

  it('TC-03-06-01 edit response เมื่อกดปุ่ม response จะเข้าไปหน้า responset', () => {
    cy.wait(2000);
    cy.get(':nth-child(6) > .q-gutter-x-sm > :nth-child(3)').click();
    cy.url().should('include', '/quiz/1307/edit#replies');
  });
});
