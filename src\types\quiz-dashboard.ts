export interface AssessmentMeta {
  assessmentName: string;
  uniqueUsers: number;
  highestScore: number;
  lowestScore: number;
}

export interface ChartData {
  labels: string[];
  datasets: {
    data: number[];
    backgroundColor: string[];
  }[];
}

export interface QuestionResponseData {
  questionId: number;
  orderInQuiz: number;
  questionText: string;
  questionType: string;
  options: OptionResponseData[];
  chartData: ChartData;
}

export interface OptionResponseData {
  optionId: number;
  orderInQuestion: number;
  optionText: string;
  selectionCount: number;
  isCorrectAnswer: boolean;
}

export interface ParticipantData {
  id: number;
  date: string;
  userName: string;
  score: number;
}

export interface ParticipantDetails {
  submissionId: number;
  assessmentId: number;
  assessmentName: string;
  userId: number;
  userName: string;
  startTime: string;
  endTime: string;
  totalScore: number;
  maxScore: number;
  scorePercentage: number;
  questions: QuestionDetail[];
}

export interface QuestionDetail {
  questionId: number;
  questionText: string;
  questionSequence: number;
  section: number;
  questionType: string; // Added to support question type identification
  selectedOptionId?: number;
  selectedOptionText?: string;
  textAnswer?: string; // Added to support TEXTFIELD answers
  isCorrect: boolean;
  score: number;
  options: OptionDetail[];
}

export interface OptionDetail {
  id: number;
  text: string;
  sequence: number;
  value: number;
  isSelected: boolean;
}
