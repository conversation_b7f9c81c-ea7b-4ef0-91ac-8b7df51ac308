<template>
  <q-page padding>
    <div class="text-h6 q-mb-md">จัดการแบบประเมิน</div>

    <!-- แถบบน: Search + เพิ่ม -->
    <div class="row items-center q-gutter-sm justify-end q-mb-md">
      <SearchBar @search="onSearchUpdate" />
      <q-btn label="สร้าง" color="accent" icon="add" @click="onClickCreate"> </q-btn>
    </div>
    <!-- <div class="row items-center justify-between q-mb-md">
      <div class="text-h6 q-mb-md">จัดการแบบประเมิน</div>

      <div class="row items-center q-gutter-sm">
        <SearchBar @search="onSearchUpdate" />
        <q-btn
          label="สร้าง"
          icon="add"
          class="text-white"
          color="accent"
          s
          @click="onClickCreate"
        />
      </div>
    </div> -->

    <q-table
      :rows="filteredFormData"
      :columns="evaluateManagementColumns"
      row-key="id"
      flat
      bordered
      separator="cell"
      :loading="loading"
      v-model:pagination="pagination"
      @request="handleRequest"
      binary-state-sort
    >
      <!-- Date -->
      <template v-slot:body-cell-date="{ row }">
        <q-td class="text-center">{{ row.date }}</q-td>
      </template>

      <!-- Link -->
      <template v-slot:body-cell-link="{ row }">
        <q-td class="text-center">
          <q-icon name="link" size="20px" class="cursor-pointer" @click="openShareDialog(row)" />
        </q-td>
      </template>

      <!-- Actions -->
      <template v-slot:body-cell-actions="{ row }">
        <q-td class="text-center">
          <div class="q-gutter-x-sm flex justify-center">
            <q-btn
              dense
              unelevated
              class="view-icon"
              icon="visibility"
              @click="onClickPreview(row)"
            />
            <q-btn dense unelevated class="edit-graph-icon" icon="edit" @click="onClickEdit(row)" />
            <q-btn
              dense
              unelevated
              class="edit-graph-icon"
              icon="bar_chart"
              @click="onClickChart(row)"
            />
            <q-btn dense unelevated class="del-icon" icon="delete" @click="deleteItem(row)" />
            <!--ปุ่มViewItem ของUser ใส่ไว้ก่อนไว้ทำuserตอบ รอมีหน้าสำหรับroleUser ค่อยลบ-->
            <q-btn dense unelevated class="view-icon" icon="people" @click="viewItem(row)" />
          </div>
        </q-td>
      </template>
    </q-table>

    <ConfirmDialog
      v-model="confirmDialogVisible"
      :title="titleDialog"
      @confirm="onConfirmDelete"
      @cancel="onCancelDelete"
    />
  </q-page>
</template>

<script setup lang="ts">
// import EvaluateTable from 'src/components/evaluate/EvaluateTable.vue';
import SearchBar from 'src/components/SearchBar.vue';
import router from 'src/router';
import { AssessmentService } from 'src/services/asm/assessmentService';
import { useAuthStore } from 'src/stores/auth';
import { useBlockCreatorStore } from 'src/stores/block_creator';
import { api } from 'src/boot/axios';
import type { ItemBlock } from 'src/types/models';
import { ref, computed, onMounted, defineAsyncComponent } from 'vue';
import ConfirmDialog from 'src/components/ConfirmDialog.vue';
import { evaluateManagementColumns } from 'src/data/table_columns';
import { defaultPaginationValue } from 'src/configs/app.config';
import type { Assessment } from 'src/types/models';
import { useQuasar, type QTableProps } from 'quasar';

const confirmDialogVisible = ref(false);
const selectedRowToDelete = ref<Assessment | null>(null);
const titleDialog = ref('');
const $q = useQuasar();
const formData = ref<Assessment[]>([]);
const search = ref('');
const pagination = ref<QTableProps['pagination']>({ ...defaultPaginationValue });
const loading = ref(false);

const filteredFormData = computed(() =>
  formData.value.filter((item) => item.name?.toLowerCase().includes(search.value.toLowerCase())),
);
async function onClickCreate() {
  try {
    const user = useAuthStore().getCurrentUser();
    const blockCreatorStore = useBlockCreatorStore();

    // Step 1: Create Assessment
    const assessmentResponse = await new AssessmentService('evaluate').createOne({
      creatorUserId: user?.id || 1,
      programId: 1,
      type: 'EVALUATE',
    });

    // Step 2: Try to Create Header Block (Expected to fail due to backend bug)
    let headerCreated = false;
    try {
      await api.post<ItemBlock>('/item-blocks/block', {
        assessmentId: assessmentResponse.id,
        type: 'HEADER',
        sequence: 1,
      });
      headerCreated = true;
    } catch {
      // Header creation failed - expected due to backend bug
    }

    // Step 3: Create Radio ItemBlock (Should work)
    await api.post<ItemBlock>('/item-blocks/block', {
      assessmentId: assessmentResponse.id,
      type: 'RADIO',
      sequence: headerCreated ? 2 : 1,
    });

    // Store the created assessment immediately in the block creator store
    blockCreatorStore.currentAssessment = assessmentResponse;

    // CRITICAL: Fetch the complete assessment data including itemBlocks before navigation
    try {
      await blockCreatorStore.fetchAssessmentById(assessmentResponse.id);

      // Validate that we have itemBlocks before proceeding
      if (
        !blockCreatorStore.currentAssessment?.itemBlocks ||
        blockCreatorStore.currentAssessment.itemBlocks.length === 0
      ) {
        // No itemBlocks found - BlockCreator will handle this
      }
    } catch {
      // Failed to fetch complete assessment data
    }

    await router.push({
      name: 'evaluate-edit',
      query: { mode: 'edit' },
      params: { id: assessmentResponse.id },
      hash: '#questions',
    });
  } catch (error) {
    console.error('Failed to create evaluation:', error);
    console.error('Error details:', JSON.stringify(error, null, 2));
  }
}
// async function onClickCreate() {
//   const user = useAuthStore().getCurrentUser();
//   const res = await new AssessmentService('evaluate').createOne({
//     creatorUserId: user?.id || 1,
//     programId: 1,
//     type: 'EVALUATE',
//   });
//   await router.push({
//     name: 'evaluate-edit',
//     params: { id: res.id },
//     hash: '#questions',
//   });
// }

function openShareDialog(row: Assessment) {
  $q.dialog({
    component: defineAsyncComponent(() => import('src/components/common/ShareLinkDialog.vue')),
    componentProps: {
      resource: 'evaluate',
      genLink: row.linkURL,
    },
    persistent: true,
  });
}

function deleteItem(row: Assessment) {
  selectedRowToDelete.value = row;
  titleDialog.value = `ยืนยันการลบแบบประเมิน ${row.name}`;
  confirmDialogVisible.value = true;
}

async function onConfirmDelete() {
  if (!selectedRowToDelete.value) return;
  try {
    await new AssessmentService('evaluate').deleteOne(selectedRowToDelete.value.id);
    await fetchDataRow(pagination.value);
  } catch (error) {
    console.error(error);
  } finally {
    selectedRowToDelete.value = null;
  }
}

function onCancelDelete() {
  selectedRowToDelete.value = null;
}

async function onSearchUpdate(keyword: string) {
  search.value = keyword;
  await fetchDataRow(pagination.value);
}

async function fetchDataRow(pag: QTableProps['pagination']) {
  loading.value = true;
  try {
    const res = await new AssessmentService('evaluate').fetchAll(pag, search.value);
    formData.value = res.data;
    pagination.value!.rowsNumber = res.total;
  } catch (error) {
    console.error('Failed to fetch assessments:', error);
  } finally {
    loading.value = false;
  }
}

const handleRequest: QTableProps['onRequest'] = ({ pagination: pag }) => {
  if (pag) pagination.value = pag;
  void fetchDataRow(pag);
};

async function onClickPreview(row: Assessment) {
  await router.push({
    name: 'evaluate-preview',
    params: { id: row.id, section: 1 },
    query: { id: row.id },
  });
}

async function onClickEdit(row: Assessment) {
  await router.push({
    name: 'evaluate-edit',
    params: { id: row.id.toString() },
    hash: '#questions',
  });
}

async function onClickChart(row: Assessment) {
  await router.push({ name: 'evaluate-edit', params: { id: row.id.toString() }, hash: '#replies' });
}

async function viewItem(row: Assessment) {
  await router.push({
    name: 'evaluate-do',
    params: { url: row.linkURL, section: 1 },
    query: { id: row.id },
  });
}

onMounted(async () => {
  await fetchDataRow(pagination.value);
});
</script>

<style scoped>
.view-icon {
  background-color: #39303d;
  color: white;
  border-radius: 12px;
}

.edit-graph-icon {
  background-color: var(--q-accent);
  color: white;
  border-radius: 12px;
}

.del-icon {
  background-color: #ab2433;
  color: white;
  border-radius: 12px;
}

:deep(.q-table thead th) {
  font-size: 20px;
}

:deep(.q-table tbody td) {
  font-size: 18px;
}
</style>
