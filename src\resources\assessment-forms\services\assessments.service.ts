import {
  Injectable,
  NotFoundException,
  BadRequestException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { v4 as uuidv4 } from 'uuid';
import { Assessment } from '../entities/assessment.entity';
import { ItemBlock } from '../entities/item-block.entity';
import { CreateAssessmentDto } from '../dto/creates/create-assessment.dto';
import { UpdateAssessmentDto } from '../dto/updates/update-assessment.dto';
import { AssessmentType } from '../enums/assessment-type.enum';
import { AssessmentConfig } from 'src/configs/assessment.config';
import { AssessmentValidator } from '../helper/assessments.validator';
import { AssessmentHelper } from '../helper/assessments.helper';
import type { DataParams, DataResponse } from 'src/types/params';
import type { User } from 'src/resources/users/entities/user.entity';
import type { Program } from 'src/resources/programs/entities/program.entity';
@Injectable()
export class AssessmentsService {
  constructor(
    @InjectRepository(Assessment)
    private assessmentRepository: Repository<Assessment>,
    @InjectRepository(ItemBlock)
    private itemBlockRepository: Repository<ItemBlock>,
    private readonly assessmentValidator: AssessmentValidator,
    private readonly assessmentHelper: AssessmentHelper,
  ) {}

  // === PUBLIC METHODS ===

  async createOne(dto: CreateAssessmentDto): Promise<Assessment> {
    this.assessmentValidator.validateCreateDto(dto);

    const saved = await this.assessmentRepository.save({
      name: AssessmentConfig.FORM_UNTITLED,
      type: dto.type,
      linkURL: uuidv4(),
      creator: { id: dto.creatorUserId } as User,
      program: { id: dto.programId } as Program,
    });

    return this.assessmentHelper.buildAssessmentResponse(saved);
  }

  async getAll(
    query: DataParams,
    type: AssessmentType,
  ): Promise<DataResponse<Assessment>> {
    const qb = this.assessmentHelper.createBaseQueryBuilder(type);
    this.assessmentHelper.applySearchFilter(qb, query.search);
    this.assessmentHelper.applySorting(qb, query.sortBy, query.order);

    return this.assessmentHelper.executePagedQuery(qb, query);
  }

  async getAllEditor(
    query: DataParams,
    type: AssessmentType,
    user: any,
  ): Promise<DataResponse<Assessment>> {
    const qb = this.assessmentHelper.createBaseQueryBuilder(type);
    qb.andWhere('assessment.creator = :creatorId', { creatorId: user.id });
    this.assessmentHelper.applySearchFilter(qb, query.search);
    this.assessmentHelper.applySorting(qb, query.sortBy, query.order);

    return this.assessmentHelper.executePagedQuery(qb, query);
  }

  async getAllPrototypes(
    type: AssessmentType,
    query: DataParams,
  ): Promise<DataResponse<Assessment>> {
    const qb = this.assessmentHelper.createBaseQueryBuilder(type);
    qb.andWhere('assessment.isPrototype = :isPrototype', { isPrototype: true });
    this.assessmentHelper.applySearchFilter(qb, query.search);
    this.assessmentHelper.applySorting(qb, query.sortBy, query.order);

    return this.assessmentHelper.executePagedQuery(qb, query);
  }

  async getAllUser(
    query: DataParams,
    type: AssessmentType,
  ): Promise<DataResponse<Assessment>> {
    const qb = this.assessmentHelper.createBaseQueryBuilder(type);
    qb.andWhere('assessment.endAt >= :currentDate', {
      currentDate: new Date(),
    }).andWhere('assessment.status = :status', { status: true });
    this.assessmentHelper.applySearchFilter(qb, query.search);
    this.assessmentHelper.applySorting(qb, query.sortBy, query.order);

    return this.assessmentHelper.executePagedQuery(qb, query);
  }

  async findOne(id: number): Promise<Assessment> {
    // Run validation and data fetching in parallel for better performance
    const [_, fullAssessment] = await Promise.all([
      this.assessmentValidator.validateAssessment(id),
      this.assessmentHelper.findAssessmentWithRelations(id),
    ]);

    if (!fullAssessment) {
      throw new NotFoundException(`Assessment with ID ${id} not found`);
    }

    await this.assessmentHelper.processAssessmentImages(fullAssessment);
    return fullAssessment;
  }

  async findOneBySection(id: number, section: number) {
    await this.assessmentValidator.validateAssessment(id);

    const assessment = await this.assessmentHelper.findAssessmentBySection(
      id,
      section,
    );

    if (assessment) {
      await this.assessmentHelper.processAssessmentImages(assessment);
    }

    return assessment;
  }

  async getAllForUserStandard(
    query: DataParams,
    type: AssessmentType,
  ): Promise<DataResponse<Assessment>> {
    const qb = this.assessmentHelper.createBaseQueryBuilder(type);
    qb.andWhere('assessment.endAt >= :currentDate', {
      currentDate: new Date(),
    }).andWhere('assessment.status = :status', { status: true });

    this.assessmentHelper.applySearchFilter(qb, query.search);
    this.assessmentHelper.applySorting(qb, query.sortBy, query.order);

    const result = await this.assessmentHelper.executePagedQuery(qb, query);
    // Map id ให้เป็น running id ตามลำดับในหน้านั้น
    const startId = ((query.page ?? 1) - 1) * (query.limit ?? 10) + 1;
    const mappedData = result.data.map((item, idx) => ({
      ...item,
      id: startId + idx,
    }));
    return {
      ...result,
      data: mappedData,
    };
  }

  async getByURL(uuid: string): Promise<Assessment> {
    const assessment =
      await this.assessmentHelper.findAssessmentByLinkURL(uuid);

    if (!assessment) {
      throw new NotFoundException(
        `Assessment with linkURL "${uuid}" not found`,
      );
    }

    await this.assessmentHelper.processAssessmentImages(assessment);
    return assessment;
  }

  async update(
    id: number,
    updateDto: UpdateAssessmentDto,
  ): Promise<Assessment> {
    this.assessmentValidator.validateUpdateDto(updateDto);

    // Run validation and data fetching in parallel
    const [_, fullAssessment] = await Promise.all([
      this.assessmentValidator.validateAssessment(id),
      this.assessmentHelper.findAssessmentWithRelations(id),
    ]);

    if (!fullAssessment) {
      throw new NotFoundException(`Assessment with ID ${id} not found`);
    }

    this.applyUpdates(fullAssessment, updateDto);

    const updatedAssessment =
      await this.assessmentRepository.save(fullAssessment);
    return updatedAssessment;
  }

  async remove(id: number) {
    const assessment = await this.assessmentValidator.validateAssessment(id);
    await this.assessmentRepository.remove(assessment);
    return { success: true };
  }

  async duplicateAssessmentTemplate(
    sourceId: number,
    targetId: number,
  ): Promise<any> {
    const { sourceAssessment, targetAssessment } =
      await this.getAssessmentsForDuplication(sourceId, targetId);

    await this.assessmentHelper.clearAssessmentBlocks(targetId);
    const newItemBlocks = this.assessmentHelper.duplicateItemBlocks(
      sourceAssessment.itemBlocks,
      targetAssessment,
    );

    const saved = await this.assessmentRepository.save({
      ...targetAssessment,
      itemBlocks: newItemBlocks,
    });

    return this.assessmentHelper.buildDuplicationResponse(saved);
  }

  async getDashboard(type: AssessmentType) {
    return this.assessmentHelper.getAssessmentCount(type);
  }

  // === PRIVATE HELPER METHODS ===

  private applyUpdates(
    assessment: Assessment,
    updateDto: UpdateAssessmentDto,
  ): void {
    Object.entries(updateDto).forEach(([key, value]) => {
      const processedValue = this.assessmentValidator.validateFieldUpdate(
        key,
        value,
        assessment,
      );
      if (processedValue !== undefined) {
        (assessment as any)[key] = processedValue;
      }
    });
  }

  private async getAssessmentsForDuplication(
    sourceId: number,
    targetId: number,
  ) {
    const { sourceAssessment, targetAssessment } =
      await this.assessmentValidator.validateAssessmentsForDuplication(
        sourceId,
        targetId,
      );

    const sourceWithRelations =
      await this.assessmentHelper.findAssessmentWithRelations(sourceId);

    this.assessmentValidator.validateDuplicationData(sourceWithRelations);

    return { sourceAssessment: sourceWithRelations, targetAssessment };
  }
}
