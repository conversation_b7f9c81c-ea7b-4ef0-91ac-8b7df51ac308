import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseInterceptors,
  UploadedFile,
  UseGuards,
} from '@nestjs/common';
import { ImageBodiesService } from './image-bodies.service';

import { ApiTags, ApiOperation, ApiConsumes, ApiBody, ApiBearerAuth } from '@nestjs/swagger';
import { diskStorage } from 'multer';
import { extname } from 'path';
import { v4 as uuidv4 } from 'uuid';
import { FileInterceptor } from '@nestjs/platform-express';
import type { UpdateImageBodyDto } from '../dto/updates/update-image-body.dto';
import type { CreateImageBodyDto } from '../dto/creates/create-image-body.dto';
import { AuthGuard } from 'src/auth/auth.guard';

@ApiTags('Image-Bodies')
@ApiBearerAuth()
@UseGuards(AuthGuard)
@Controller('image-bodies')
export class ImageBodiesController {
  constructor(private readonly imageBodiesService: ImageBodiesService) {}

  @Post()
  @ApiOperation({
    summary: 'สร้าง Image Bodyใหม่',
    description: 'สร้าง Image Body ใหม่สำหรับ (Evaluate)',
  })
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    description: 'ข้อมูลสำหรับสร้าง Image Body ใหม่',
    schema: {
      type: 'object',
      properties: {
        itemBlockId: { type: 'integer', example: 1 },
        imageText: { type: 'string', example: 'ภาพประกอบ' },
        imageWidth: { type: 'integer', default: '' },
        imageHeight: { type: 'integer', default: '' },
        imagePath: {
          type: 'string',
          format: 'binary',
        },
      },
      required: ['itemBlockId'],
    },
  })
  @UseInterceptors(
    FileInterceptor('imagePath', {
      storage: diskStorage({
        destination: './uploaded_file',
        filename: (req, file, cb) => {
          const ext = extname(file.originalname);
          const name = uuidv4();
          cb(null, name + ext);
        },
      }),
    }),
  )
  create(
    @Body() createImageBodyDto: CreateImageBodyDto,
    @UploadedFile() file: Express.Multer.File,
  ) {
    return this.imageBodiesService.create(createImageBodyDto, file);
  }

  @Get()
  findAll() {
    return this.imageBodiesService.findAll();
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.imageBodiesService.findOne(+id);
  }

  @Patch(':id')
  @ApiOperation({
    summary: 'อัปเดตImage Body',
    description: 'อัปเดตImage Body (Evaluate) ตาม template ที่กำหนด',
  })
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    description: 'ข้อมูลสำหรับอัปเดตImage Body (แบบฟอร์ม)',
    schema: {
      type: 'object',
      properties: {
        itemBlockId: { type: 'integer', example: 1 },
        imageText: { type: 'string', example: 'ภาพประกอบ' },
        imageWidth: { type: 'integer', default: '' },
        imageHeight: { type: 'integer', default: '' },
        imagePath: {
          type: 'string',
          format: 'binary',
        },
      },
      required: ['itemBlockId'],
    },
  })
  @UseInterceptors(
    FileInterceptor('imagePath', {
      storage: diskStorage({
        destination: './uploaded_file',
        filename: (req, file, cb) => {
          const ext = extname(file.originalname);
          const name = uuidv4();
          cb(null, name + ext);
        },
      }),
    }),
  )
  update(
    @Param('id') id: string,
    @Body() updateImageBodyDto: UpdateImageBodyDto,
    @UploadedFile() file: Express.Multer.File,
  ) {
    return this.imageBodiesService.update(+id, updateImageBodyDto, file);
  }

  @Delete(':id')
  remove(@Param('id') id: number) {
    return this.imageBodiesService.remove(+id);
  }
}
