/// <reference types="cypress" />
import 'cypress-file-upload';

declare global {
  // eslint-disable-next-line @typescript-eslint/no-namespace
  namespace Cypress {
    interface Chainable {
      login(email: string, password: string): Chainable<void>;
      logout(): Chainable<void>;
      enterQuiz(): Chainable<void>;
      searchQuizFormName(name: string): Chainable<void>;
      clickEdit(): Chainable<void>;
      attachFile(filePath: string, options?: Partial<{ subjectType: string }>): Chainable<Element>;
    }
  }
}

Cypress.Commands.add('login', (email: string, password: string) => {
  cy.visit('http://localhost:9000/login');
  cy.get('[data-cy="login_username"]').type(email);
  cy.get('[data-cy="login_password"]').type(password);
  cy.get('[data-cy="login_btn"]').click();
});

Cypress.Commands.add('logout', () => {
  cy.get('.q-avatar__content > img').click();
  cy.get('.q-item--clickable').contains('ออกจากระบบ').click();
});

Cypress.Commands.add('enterQuiz', () => {
  cy.get(':nth-child(2) > .q-card > .q-card__section').click();
});

Cypress.Commands.add('searchQuizFormName', (name: string) => {
  cy.get('.q-gutter-sm > .q-field > .q-field__inner > .q-field__control').type(name);
  cy.get('[style="min-width: 250px; white-space: normal;"]').should('contain', name);
});
Cypress.Commands.add('clickEdit', () => {
  cy.get(':nth-child(6) > .q-gutter-x-sm > :nth-child(2)').should('be.visible');
  cy.wait(2000);
  cy.get(':nth-child(6) > .q-gutter-x-sm > :nth-child(2)').click();
});
