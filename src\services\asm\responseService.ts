import { api } from 'src/boot/axios';
import type { Response } from 'src/types/models';
import type { ChartData } from 'src/types/chart';
import { isAxiosError } from 'axios';
import { Notify } from 'quasar';

const showError = (message: string) => {
  Notify.create({
    message,
    type: 'negative',
    position: 'bottom',
    timeout: 3000,
  });
};

export class ResponsesService {
  private path = '/responses';

  async create(params: Response): Promise<Response> {
    try {
      const res = await api.post<Response>(this.path, params);
      return res.data;
    } catch {
      showError('ส่งคำตอบล้มเหลว');
      throw new Error('Create response failed');
    }
  }

  async findAll(): Promise<Response[]> {
    const res = await api.get<Response[]>(this.path);
    return res.data;
  }

  async findOne(id: number): Promise<Response> {
    const res = await api.get<Response>(`${this.path}/${id}`);
    return res.data;
  }

  async findAnswer(submissionId: number, questionId: number): Promise<{ data?: Response }> {
    try {
      return await api.get<Response>(`${this.path}/${submissionId}/${questionId}`);
    } catch (error: unknown) {
      console.warn('findAnswer fallback to empty object due to error:', error);
      return {};
    }
  }

  async findAnswers(submissionId: number, questionId: number): Promise<Response[]> {
    const res = await api.get<Response[]>(`${this.path}/checkbox/${submissionId}/${questionId}`);
    return res.data;
  }

  async findRemoveCheckBoxAnswer(
    submissionId: number,
    questionId: number,
    selectedOptionId: number,
  ): Promise<Response | null> {
    try {
      const res = await api.get<Response>(
        `${this.path}/${submissionId}/${questionId}/${selectedOptionId}`,
      );
      return res.data;
    } catch (error: unknown) {
      if (isAxiosError(error) && error.response?.status === 400) {
        return null;
      }
      console.error('Unexpected error:', error);
      throw error;
    }
  }

  async update(id: number, params: Response): Promise<Response> {
    try {
      const res = await api.patch<Response>(`${this.path}/${id}`, params);
      return res.data;
    } catch {
      showError('อัปเดตคำตอบล้มเหลว');
      throw new Error('Update response failed');
    }
  }

  async remove(id: number): Promise<void> {
    try {
      await api.delete(`${this.path}/${id}`);
    } catch {
      showError('ลบคำตอบล้มเหลว');
      throw new Error('Remove response failed');
    }
  }

  async getChartData(assessmentId: number): Promise<ChartData[]> {
    const res = await api.get<ChartData[]>(`${this.path}/chart-data/${assessmentId}`);
    return res.data;
  }

  async saveUserQuizResponse(data: Response): Promise<Response> {
    try {
      const res = await api.post<Response>(`${this.path}/quiz/save-response`, data);
      return res.data;
    } catch {
      showError('ส่งคำตอบไม่สำเร็จ');
      throw new Error('Submit quiz response failed');
    }
  }

  async getNumberOfResponses(assessmentId: number): Promise<number> {
    const res = await api.get<{ number: number }>(
      `/assessments${this.path}/header/${assessmentId}`,
    );
    return res.data.number;
  }

  async getResponseById(id: number): Promise<{ data: ChartData[] }> {
    try {
      return await api.get(`assessments/dashboard/evaluate/${id}`);
    } catch (error) {
      showError('ไม่สามารถดึงข้อมูลแบบประเมินได้');
      throw error;
    }
  }

  async getResponseHeaderById(id: number) {
    try {
      return await api.get(`assessments/header/${id}`);
    } catch (error) {
      showError('ไม่สามารถดึงข้อมูลหัวข้อคำตอบได้');
      throw error;
    }
  }

  async getAssessmentToExcelById(id: number): Promise<void> {
    try {
      const response = await api.get(`assessments/${id}/export/excel`, {
        responseType: 'blob',
      });
      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement('a');
      link.href = url;
      const contentDisposition = response.headers['content-disposition'];
      let fileName = `assessment_${id}.xlsx`;
      if (contentDisposition) {
        const fileNameMatch = contentDisposition.match(/filename\*=UTF-8''(.+)$/);
        if (fileNameMatch && fileNameMatch[1]) {
          fileName = decodeURIComponent(fileNameMatch[1]);
        }
      }
      link.setAttribute('download', fileName);
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
    } catch (error) {
      showError('ไม่สามารถดึงข้อมูลหัวข้อคำตอบได้');
      throw error;
    }
  }
}
