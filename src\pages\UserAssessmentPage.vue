<template>
  <q-page padding>
    <div class="text-h6 q-mb-md">{{ pageTitle }}</div>
    <div class="row items-center q-gutter-sm justify-end q-mb-md">
      <SearchBar @search="onSearchUpdate" />
    </div>
    <q-table
      :rows="rows"
      :columns="assessmentColumns"
      row-key="id"
      flat
      bordered
      wrap-cells
      separator="cell"
      :loading="Loading.isActive"
      v-model:pagination="pagination"
      @request="handleRequest"
      binary-state-sort
    >
      <template v-slot:body-cell-actions="{ row }">
        <q-td class="text-center">
          <div class="q-gutter-x-sm flex justify-center">
            <q-btn dense unelevated class="view-icon" icon="article" @click="onClickPreview(row)" />
          </div>
        </q-td>
      </template>
    </q-table>
    <ConfirmDialog
      v-model="confirmDialogVisible"
      :title="titleDialog"
      @confirm="onConfirmDelete"
      @cancel="onCancelDelete"
    />
  </q-page>
</template>

<script setup lang="ts">
import { onMounted, ref, computed } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useQuasar, Loading } from 'quasar';
import { quizUserColumns } from 'src/data/table_columns';
import { defaultPaginationValue } from 'src/configs/app.config';
import type { Assessment } from 'src/types/models';
import SearchBar from 'src/components/SearchBar.vue';
import ConfirmDialog from 'src/components/ConfirmDialog.vue';
import type { QTableProps } from 'quasar';
import { AssessmentService } from 'src/services/asm/assessmentService';
import { useGlobalStore } from 'src/stores/global';

const globalStore = useGlobalStore();
const router = useRouter();
const route = useRoute();
const pagination = ref<QTableProps['pagination']>({ ...defaultPaginationValue });
const search = ref<string>('');
const rows = ref<Assessment[]>([]);
const confirmDialogVisible = ref(false);
const selectedRowToDelete = ref<Assessment | null>(null);
const titleDialog = ref('');

const type = computed<'quiz' | 'evaluate'>(() => {
  const t = route.meta.type || route.params.type || 'quiz';
  return t === 'evaluate' ? 'evaluate' : 'quiz';
});
const pageTitle = computed(() =>
  type.value === 'evaluate' ? 'แบบสอบถามทั้งหมด' : 'แบบทดสอบทั้งหมด',
);
const assessmentColumns = computed(() => quizUserColumns); // You can switch columns if needed

async function onSearchUpdate(keyword: string) {
  const res = await new AssessmentService(type.value).fetchAllStUser(pagination.value, keyword);
  if (res) {
    rows.value = res.data;
    pagination.value!.rowsNumber = res.total;
  }
}

const { notify } = useQuasar();

const fetchDataRow = async (_pag: QTableProps['pagination']) => {
  const res = await new AssessmentService(type.value).fetchAllStUser(_pag, search.value);
  if (res) {
    rows.value = res.data;
    pagination.value!.rowsNumber = res.total;
  }
};

const handleRequest: QTableProps['onRequest'] = ({ pagination: _pag }) => {
  if (_pag) {
    pagination.value = _pag;
  }
  fetchDataRow(_pag).catch((error) => {
    console.error('Failed to fetch assessments:', error);
    notify({
      type: 'negative',
      message: 'เกิดข้อผิดพลาดในการดึงข้อมูล',
    });
  });
};
async function onClickPreview(row: Assessment) {
  try {
    globalStore.setQuizTitle(row.id.toString(), row.name || `แบบทดสอบ #${row.id}`);
    if (type.value === 'evaluate') {
      await router.push({
        name: 'evaluate-do',
        params: { url: row.linkURL.toString(), section: 1 },
      });
    } else {
      await router.push({
        name: 'quiz-do',
        params: { linkUrl: row.linkURL.toString() },
      });
    }
  } catch (error) {
    console.error('Navigation to do page failed:', error);
  }
}

async function onConfirmDelete() {
  if (!selectedRowToDelete.value) return;
  try {
    await new AssessmentService(type.value).deleteOne(selectedRowToDelete.value.id);
    await fetchDataRow(pagination.value);
  } catch (error) {
    console.error(error);
  } finally {
    selectedRowToDelete.value = null;
  }
}

function onCancelDelete() {
  selectedRowToDelete.value = null;
}

onMounted(() => {
  fetchDataRow(defaultPaginationValue).catch((error) => {
    console.error('Failed to fetch initial assessments:', error);
    notify({
      type: 'negative',
      message: 'เกิดข้อผิดพลาดในการดึงข้อมูล',
    });
  });
});
</script>
<style scoped lang="scss">
:deep(.q-table thead tr) {
  background-color: var(--q-primary) !important;
  color: black !important;
}

.view-icon {
  background-color: var(--q-accent);
  color: white;
  width: 68px;
  border-radius: 12px;
}

.edit-graph-icon {
  background-color: var(--q-accent);
  color: white;
  border-radius: 12px;
}

.del-icon {
  background-color: #ab2433;
  color: white;
  border-radius: 12px;
}

:deep(.q-table thead th) {
  font-size: 20px;
}

:deep(.q-table tbody td) {
  font-size: 18px;
}
</style>
