<template>
  <div>
    <q-btn
      class="float-btn-toggle"
      icon="app:quiz"
      size="1.2rem"
      color="primary"
      round
      @click="show = !show"
      style="position: fixed; top: 100px; right: 100px; z-index: 2001"
    >
      <!-- <img src="/svg/quiz.svg" alt="quiz" style="width: 32px; height: 32px" /> -->
    </q-btn>
    <transition name="fade">
      <div v-if="show" class="float-btn-floating" ref="floatBtnRef">
        <div
          class="question-grid"
          :style="{
            gridTemplateColumns: `repeat(${gridColumns}, 1fr)`,
            gridTemplateRows: `repeat(${gridRows}, 1fr)`,
            width:
              gridColumns < maxColumns ? `${gridColumns * 62 + (gridColumns - 1) * 6}px` : '100%',
            justifyContent: gridColumns < maxColumns ? 'center' : 'unset',
          }"
        >
          <q-btn
            v-for="n in totalQuestions"
            :key="n"
            :label="n.toString()"
            :color="
              n === currentQuestion
                ? 'primary'
                : doneList && doneList[n - 1]
                  ? 'positive'
                  : 'grey-8'
            "
            class="question-btn"
            :outline="n !== currentQuestion"
            :unelevated="n === currentQuestion"
            @click="() => emit('select', n)"
          />
        </div>
      </div>
    </transition>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onBeforeUnmount } from 'vue';

const props = defineProps<{
  totalQuestions: number;
  currentQuestion?: number;
  doneList?: boolean[];
}>();
const emit = defineEmits(['select']);
const show = ref(false);
const floatBtnRef = ref<HTMLElement | null>(null);

const maxColumns = 8;
const gridColumns = computed(() => Math.min(maxColumns, props.totalQuestions));
const gridRows = computed(() => Math.ceil(props.totalQuestions / gridColumns.value) || 1);

function handleClickOutside(event: MouseEvent) {
  if (!show.value) return;
  const el = floatBtnRef.value;
  if (el && el.contains(event.target as Node)) return;
  show.value = false;
}

onMounted(() => {
  document.addEventListener('mousedown', handleClickOutside);
});
onBeforeUnmount(() => {
  document.removeEventListener('mousedown', handleClickOutside);
});
</script>

<style scoped>
.float-btn-floating {
  position: fixed;
  top: 152px;
  right: 100px;
  z-index: 2000;
  background: #fff;
  border-radius: 24px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.18);
  padding: 16px 6px 8px 16px;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  transition: box-shadow 0.2s;
  max-width: 578px;
  max-height: 408px;
}
.question-grid {
  display: grid;
  gap: 6px;
  margin: 0 auto;
  max-width: 546px;
  max-height: 250px;
  overflow-x: hidden;
  overflow-y: auto;
  justify-content: center;
  padding-right: 4px;
  padding-bottom: 16px;
  padding-top: 4px;
}
.question-btn {
  width: 100%;
  height: 100%;
  min-width: 35px;
  min-height: 44px;
  font-weight: bold;
  font-size: 1.1em;
  padding: 0;
  transition: background 0.15s;
}
.float-btn-toggle {
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.18);
  position: fixed;
  top: 32px;
  right: 32px;
  z-index: 2001;
  width: 54px;
  height: 54px;
  font-size: 1.5em;
  border-radius: 100px;
}
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.2s;
}
.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
</style>
