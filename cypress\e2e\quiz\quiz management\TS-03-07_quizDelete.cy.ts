describe('Quiz Delete', () => {
  beforeEach(() => {
    cy.fixture('users').then((user) => {
      cy.login(user.superAdmin.username, user.superAdmin.password);
    });
    cy.enterQuiz();
  });

  it('TC-03-07-01 cancel delete', () => {
    cy.get('.q-table__container').should('be.visible');
    cy.get('.q-gutter-sm > .q-btn').click();
    cy.get('[data-cy="asmTitleInput"]').clear().type('test delete quiz');
    cy.get('.q-tab-panel').click();
    cy.get('[href="/quiz/management"] > .q-item__section--side > .q-icon').click();
    cy.get(':nth-child(1) > :nth-child(6) > .q-gutter-x-sm > .del-icon').click();
    cy.get('[style="background-color: red;"]').click();
    cy.wait(3000);
    cy.get(':nth-child(1) > [style="min-width: 250px; white-space: normal;"]').should(
      'contain',
      'test delete quiz',
    );
  });

  it('TC-03-07-02 confirm delete', () => {
    cy.get('.q-table__container').should('be.visible');
    cy.get('.q-gutter-sm > .q-field > .q-field__inner > .q-field__control').type(
      'test delete quiz',
    );
    cy.get(':nth-child(1) > :nth-child(6) > .q-gutter-x-sm > .del-icon')
      .should('be.visible')
      .click();
    cy.get('.q-bg-').should('be.visible').click();
    cy.wait(3000);
    cy.get('.q-table__container').should('not.contain', 'test delete quiz');
  });
});
