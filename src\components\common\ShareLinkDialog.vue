<template>
  <q-dialog ref="dialogRef" persistent @hide="onDialogHide">
    <q-card class="q-pa-lg q-mx-md" style="width: 500px">
      <!-- QR Code -->
      <q-card-section class="text-h6 text-center"> แชร์ลิงก์ </q-card-section>
      <q-card-section class="flex">
        <q-img
          :src="qrImageLink"
          class="q-mx-auto"
          style="width: 150px; height: 150px"
          fit="contain"
        />
      </q-card-section>

      <!-- ลิงก์ -->
      <q-card-section>
        <q-input
          dense
          readonly
          v-model="endLink"
          class="q-px-sm"
          outlined
          data-cy="shareLinkDialogLink"
        >
          <template v-slot:append>
            <q-icon name="content_copy" class="cursor-pointer" @click="copyLink" />
          </template>
        </q-input>
      </q-card-section>

      <!-- ปุ่ม -->
      <q-card-actions align="center" class="">
        <q-btn
          class="q-mr-sm"
          unelevated
          label="บันทึก QR Code"
          color="secondary"
          @click="saveQR"
          style="width: 130px"
        />
        <q-btn
          unelevated
          label="ตกลง"
          color="primary"
          class="text-black"
          @click="onClickOk"
          style="width: 130px"
        />
      </q-card-actions>
    </q-card>
  </q-dialog>
</template>

<script setup lang="ts">
import { onMounted, ref } from 'vue';
import { copyToClipboard, Notify, useDialogPluginComponent } from 'quasar';
import { api } from 'src/boot/axios';

const qrImageLink = ref('');
const prefix = api.defaults['baseURL']; // Adjust this prefix as needed
const endLink = ref(`${prefix}`); // Default link value

const props = defineProps<{
  resource: string;
  genLink: string;
}>();

const { dialogRef, onDialogHide, onDialogOK } = useDialogPluginComponent();

function setupURL(resource: string, genLink: string) {
  endLink.value = `${prefix}/${resource}/` + genLink;
  qrImageLink.value =
    'https://api.qrserver.com/v1/create-qr-code/?size=150x150&data=' +
    encodeURIComponent(props.genLink);
}

onMounted(() => {
  setupURL(props.resource, props.genLink);
});

async function copyLink() {
  try {
    await copyToClipboard(endLink.value);
    Notify.create({
      message: 'คัดลอกลิงก์แล้ว',
      type: 'positive',
      position: 'top',
      timeout: 2000,
    });
  } catch {
    Notify.create({
      message: 'ไม่สามารถคัดลอกลิงก์ได้',
      type: 'negative',
      position: 'top',
      timeout: 2000,
    });
  }
}

async function saveQR() {
  try {
    const response = await fetch(qrImageLink.value);
    const blob = await response.blob();
    const url = window.URL.createObjectURL(blob);

    const a = document.createElement('a');
    a.href = url;
    a.download = 'qr-code.png';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);

    Notify.create({
      message: 'บันทึก QR Code แล้ว',
      type: 'positive',
      position: 'top',
      timeout: 2000,
    });
  } catch {
    Notify.create({
      message: 'ไม่สามารถบันทึก QR Code ได้',
      type: 'negative',
      position: 'top',
      timeout: 2000,
    });
  }
}

function onClickOk() {
  onDialogOK();
}
</script>
