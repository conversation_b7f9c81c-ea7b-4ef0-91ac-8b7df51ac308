<template>
  <q-page>
    <AsmMenuTab :menu="defaultUmsTabsMenu" />
    <div v-if="activeTabName" class="q-pa-md">
      <q-tab-panels v-model="activeTabName" animated>
        <q-tab-panel v-for="(tab, index) in defaultUmsTabsMenu" :key="index" :name="tab.name">
          <component
            :is="componentMap[tab.name as string]"
            v-if="route.hash.slice(1) === tab.name"
          />
        </q-tab-panel>
      </q-tab-panels>
    </div>
    <div v-else class="q-pa-lg text-center">
      <div class="q-mx-auto q-mt-xl text-h6 text-grey-6">กรุณาเลือกเมนูด้านบน</div>
    </div>
  </q-page>
</template>

<script setup lang="ts">
import AsmMenuTab from 'src/components/common/AsmMenuTab.vue';
import { defaultUmsTabsMenu } from 'src/data/menu';
import { defineAsyncComponent, computed } from 'vue';
import { useRoute, useRouter } from 'vue-router';

const route = useRoute();
const router = useRouter();

const activeTabName = computed({
  get: () => {
    // Get tab name by removing '#' from route.hash
    return route.hash ? route.hash.substring(1) : '';
  },
  set: (newName) => {
    // Update route.hash when q-tab-panels changes the tab (e.g., via swipe)
    // Ensure newName is not empty and hash actually needs changing
    if (newName && route.hash !== `#${newName}`) {
      void router.push({ hash: '#' + newName });
    }
  },
});

const componentMap: Record<string, ReturnType<typeof defineAsyncComponent>> = {
  users: defineAsyncComponent(() => import('./tabs/UserManagementView.vue')),
  roles: defineAsyncComponent(() => import('./tabs/RoleManagementView.vue')),
  permissions: defineAsyncComponent(() => import('./tabs/PermManagementView.vue')),
};
</script>

<style scoped></style>
