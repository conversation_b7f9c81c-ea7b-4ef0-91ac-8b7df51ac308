import {
  Controller,
  Get,
  Post,
  Patch,
  Delete,
  Body,
  Param,
  ParseIntPipe,
  UseInterceptors,
  UploadedFile,
  HttpException,
  HttpStatus,
  UseGuards,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiConsumes,
  ApiBody,
  ApiProperty,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { OptionsService } from './options.service';
import { CreateOptionDto } from '../dto/creates/create-option.dto';
import { UpdateOptionDto } from '../dto/updates/update-option.dto';
import type { Option } from '../entities/option.entity';
import { diskStorage } from 'multer';
import { extname } from 'path';
import { v4 as uuidv4 } from 'uuid';
import { AuthGuard } from 'src/auth/auth.guard';

@ApiTags('ASM - Options')
@ApiBearerAuth()
@UseGuards(AuthGuard)
@Controller('options')
export class OptionsController {
  constructor(private readonly optionsService: OptionsService) {}
  @Post()
  @ApiOperation({ summary: 'สร้างตัวเลือกใหม่' })
  @ApiBody({
    type: CreateOptionDto,
    description: 'ข้อมูลสร้าง Option ใหม่ (แบบฟอร์ม)',
  })
  @ApiConsumes('multipart/form-data')
  @UseInterceptors(
    FileInterceptor('imagePath', {
      storage: diskStorage({
        destination: './uploaded_file',
        filename: (req, file, cb) => {
          const ext = extname(file.originalname);
          const name = uuidv4();
          cb(null, name + ext);
        },
      }),
    }),
  )
  async create(
    @Body() createOptionDto: CreateOptionDto,
    @UploadedFile() file?: Express.Multer.File,
  ) {
    return await this.optionsService.create(createOptionDto, file);
  }

  @Get()
  @ApiOperation({ summary: 'ดึงข้อมูลตัวเลือกทั้งหมด' })
  @ApiResponse({ status: 200, description: 'ดึงข้อมูลสำเร็จ' })
  async findAll(): Promise<Option[]> {
    return this.optionsService.findAll();
  }

  @Get(':id')
  @ApiOperation({ summary: 'ดึงข้อมูลตัวเลือกตาม ID' })
  @ApiResponse({ status: 200, description: 'ดึงข้อมูลสำเร็จ' })
  @ApiResponse({ status: 404, description: 'ไม่พบตัวเลือก' })
  async findOne(@Param('id', ParseIntPipe) id: number): Promise<Option> {
    const option = await this.optionsService.findOne(id);
    if (!option) {
      throw new HttpException('Option not found', HttpStatus.NOT_FOUND);
    }
    return option;
  }

  @Patch(':id')
  @ApiOperation({ summary: 'แก้ไขข้อมูล<|im_start|>' })
  @ApiResponse({ status: 200, description: 'แก้ไขข้อมูลสำเร็จ' })
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    type: UpdateOptionDto,
    description: 'ข้อมูลแก้ไข Option (แบบฟอร์ม)',
  })
  @UseInterceptors(FileInterceptor('file'))
  async update(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateOptionDto: UpdateOptionDto,
    @UploadedFile() file?: Express.Multer.File,
  ): Promise<Option> {
    try {
      return await this.optionsService.update(id, updateOptionDto, file);
    } catch (error) {
      throw new HttpException(error.message, HttpStatus.BAD_REQUEST);
    }
  }

  @Delete(':id')
  @ApiOperation({ summary: 'ลบตัวเลือก' })
  @ApiResponse({ status: 200, description: 'ลบข้อมูลสำเร็จ' })
  async remove(@Param('id', ParseIntPipe) id: number): Promise<void> {
    return this.optionsService.remove(id);
  }

  @Delete(':imagePath')
  @ApiOperation({ summary: 'ลบตัวเลือก' })
  @ApiResponse({ status: 200, description: 'ลบข้อมูลสำเร็จ' })
  async removeByFileName(@Param('imagePath') imagePath: string): Promise<void> {
    const decodedPath = decodeURIComponent(imagePath);
    return this.optionsService.removeByImagePath(decodedPath);
  }

  @Delete('item-block/:itemBlockId')
  @ApiOperation({ summary: 'ลบตัวเลือกทั้งหมดของ item block' })
  @ApiResponse({ status: 200, description: 'ลบข้อมูลสำเร็จ' })
  async removeByItemBlockId(
    @Param('itemBlockId', ParseIntPipe) itemBlockId: number,
  ): Promise<void> {
    return this.optionsService.removeByItemBlockId(itemBlockId);
  }

  // New endpoints for question-based operations
  @Post(':questionId')
  @ApiOperation({ summary: 'สร้างตัวเลือกใหม่สำหรับคำถาม' })
  @ApiBody({
    type: CreateOptionDto,
    description:
      'ข้อมูลสร้าง Option ใหม่สำหรับคำถาม (แบบฟอร์ม) - ต้องส่ง itemBlockId ใน request body',
  })
  @ApiConsumes('multipart/form-data')
  @UseInterceptors(FileInterceptor('file'))
  async createForQuestion(
    @Param('questionId', ParseIntPipe) questionId: number,
    @Body() createOptionDto: CreateOptionDto,
    @UploadedFile() file?: Express.Multer.File,
  ): Promise<Option> {
    try {
      // Validate that itemBlockId is provided in the request body
      if (!createOptionDto.itemBlockId) {
        throw new HttpException(
          'itemBlockId is required in request body',
          HttpStatus.BAD_REQUEST,
        );
      }

      const defaultValues = {
        optionText: '',
        imagePath: null,
        value: 1,
        nextSection: null,
      };

      const mergedDto = {
        ...defaultValues,
        ...createOptionDto,
      };

      return await this.optionsService.createForQuestion(
        questionId,
        mergedDto,
        file,
      );
    } catch (error) {
      throw new HttpException(error.message, HttpStatus.BAD_REQUEST);
    }
  }

  @Patch(':questionId/:optionId')
  @ApiOperation({ summary: 'แก้ไขตัวเลือกสำหรับคำถาม' })
  @ApiResponse({ status: 200, description: 'แก้ไขข้อมูลสำเร็จ' })
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    type: UpdateOptionDto,
    description:
      'ข้อมูลแก้ไข Option สำหรับคำถาม (แบบฟอร์ม) - ต้องส่ง itemBlockId ใน request body',
  })
  @UseInterceptors(FileInterceptor('file'))
  async updateForQuestion(
    @Param('questionId', ParseIntPipe) questionId: number,
    @Param('optionId', ParseIntPipe) optionId: number,
    @Body() updateOptionDto: UpdateOptionDto,
    @UploadedFile() file?: Express.Multer.File,
  ): Promise<Option> {
    try {
      // Validate that itemBlockId is provided in the request body
      if (!updateOptionDto.itemBlockId) {
        throw new HttpException(
          'itemBlockId is required in request body',
          HttpStatus.BAD_REQUEST,
        );
      }

      return await this.optionsService.updateForQuestion(
        questionId,
        optionId,
        updateOptionDto,
        file,
      );
    } catch (error) {
      throw new HttpException(error.message, HttpStatus.BAD_REQUEST);
    }
  }
}
