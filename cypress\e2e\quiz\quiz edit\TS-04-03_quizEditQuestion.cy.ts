import '@4tw/cypress-drag-drop';
import 'cypress-file-upload';
import '@types/cypress-file-upload';

describe('Quiz Edit Question', () => {
  beforeEach(() => {
    cy.fixture('users').then((user) => {
      cy.login(user.superAdmin.username, user.superAdmin.password);
    });
    cy.enterQuiz();
    cy.searchQuizFormName('ฟอร์มไว้สำหรับ Test Cypress');
  });

  //   it('TC-04-02-01 edit question', () => {
  //     cy.clickEdit();
  //     cy.get(
  //       ':nth-child(2) > :nth-child(1) > .block-container > .block-content > .q-card > :nth-child(2) > :nth-child(1) > [data-v-f912b43d=""] > .input-wrapper > .editable-div',
  //     )
  //       .clear()
  //       .type('question1');
  //     cy.get('.q-pa-md').click({ force: true });
  //     cy.get('[href="/quiz/management"] > .q-item__section--side > .q-icon').click();
  //     cy.searchQuizFormName('ฟอร์มไว้สำหรับ Test Cypress');
  //     cy.clickEdit();
  //     cy.get(
  //       ':nth-child(2) > :nth-child(1) > .block-container > .block-content > .q-card > :nth-child(2) > :nth-child(1) > [data-v-f912b43d=""] > .input-wrapper > .editable-div',
  //     ).should('contain', 'question1');
  //   });

  //   it('TC-04-02-02 edit option', () => {
  //     cy.clickEdit();
  //     cy.get(
  //       ':nth-child(2) > :nth-child(1) > .block-container > .block-content > .q-card > .q-ml-md > .q-mb-sm > :nth-child(3) > .q-field > .q-field__inner > .q-field__control > .q-field__control-container > [data-cy="option-text-input-0"]',
  //     )
  //       .clear()
  //       .type('option1');
  //     cy.get('.q-pa-md').click({ force: true });
  //     cy.get('[href="/quiz/management"] > .q-item__section--side > .q-icon').click();
  //     cy.searchQuizFormName('ฟอร์มไว้สำหรับ Test Cypress');
  //     cy.clickEdit();
  //     cy.get(
  //       ':nth-child(2) > :nth-child(1) > .block-container > .block-content > .q-card > .q-ml-md > .q-mb-sm > :nth-child(3) > .q-field > .q-field__inner > .q-field__control > .q-field__control-container > [data-cy="option-text-input-0"]',
  //     ).should('have.value', 'option1');
  //   });

  // it('TC-04-02-03 change sequence of option', () => {
  //   cy.clickEdit();
  //   cy.get('[data-cy="drag-button-1"] > .q-btn__content > .q-icon').drag(
  //     '[data-cy="drag-button-0"] > .q-btn__content > .q-icon',
  //   );
  //   cy.get('[href="/quiz/management"] > .q-item__section--side').click();
  //   cy.searchQuizFormName('ฟอร์มไว้สำหรับ Test Cypress');
  //   cy.clickEdit();
  // });

  // it('TC-04-02-04 add image to question', () => {
  //   cy.clickEdit();
  //   cy.get(
  //     ':nth-child(2) > :nth-child(1) > .block-container > .block-content > .q-card > :nth-child(2) > .col-1 > .q-btn > .q-btn__content > .q-icon',
  //   ).click();
  //   cy.get('[data-cy="upload_image_btn"]').attachFile('blackWhite.png');
  //   cy.get('[style="position: relative;"] > img').should('be.visible');
  // });

  // it('TC-04-02-05 update image to question', () => {
  //   cy.clickEdit();
  //   cy.get(
  //     ':nth-child(2) > :nth-child(1) > .block-container > .block-content > .q-card > :nth-child(2) > .col-1 > .q-btn > .q-btn__content > .q-icon',
  //   ).click();
  //   cy.get('[data-cy="upload_image_btn"]').attachFile('blackWhite.png');
  // });
  // it('TC-04-02-06 add image in option', () => {
  //   cy.clickEdit();
  //   cy.get(
  //     ':nth-child(2) > :nth-child(1) > .block-container > .block-content > .q-pa-lg > .q-mb-md > .q-mb-sm > .q-ml-sm > .q-btn__content > .q-icon',
  //   ).click();
  //   cy.get('[data-cy="upload_image_btn"]').attachFile('blackWhite.png');
  //   //เขียนเพิ้มเรื่องการตรวจสอบรูปภาพหลังจากเพิ่มรูปภาพ
  // });
  // it('TC-04-02-07 update image in option', () => {
  //   cy.clickEdit();
  //   cy.get(
  //     ':nth-child(2) > :nth-child(1) > .block-container > .block-content > .q-pa-lg > .q-mb-md > .q-mb-sm > .q-ml-sm > .q-btn__content > .q-icon',
  //   ).click();
  //   cy.get('[data-cy="upload_image_btn"]').attachFile('blackWhite.png');
  //   //เขียนเพิ้มเรื่องการตรวจสอบรูปภาพหลังจากเพิ่มรูปภาพ
  // });

  // it('TC-04-02-08 delete option', () => {
  //   cy.clickEdit();
  //   cy.get(
  //     ':nth-child(2) > :nth-child(1) > .block-container > .block-content > .q-pa-lg > .q-mb-md > :nth-child(1) > :nth-child(5) > .q-btn__content > .q-icon',
  //   ).click();
  //   cy.get('[data-cy="upload_image_btn"]').attachFile('blackWhite.png');
  //   // Add verification for the uploaded image
  //   cy.get('[style="position: relative;"] > img').should('be.visible');
  // });

  it('TC-04-02-09 change type of itemBlock Radio & Checkbox (Radio -> Checkbox, Checkbox -> Radio)', () => {
    cy.clickEdit();
  });
});
