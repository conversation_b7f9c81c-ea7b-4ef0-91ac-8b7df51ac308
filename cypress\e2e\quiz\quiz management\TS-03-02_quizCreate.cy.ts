describe('Quiz Create', () => {
  beforeEach(() => {
    cy.fixture('users').then((user) => {
      cy.login(user.superAdmin.username, user.superAdmin.password);
    });
    cy.enterQuiz();
  });

  it('TC-03-02-01 create quiz', () => {
    cy.get('.q-table__container').should('be.visible');
    cy.get('.q-gutter-sm > .q-btn').click();
    cy.get('[data-cy="asmTitleInput"]').clear().type('TC-03-02-01 create quiz');
    cy.get('.q-tab-panel').click();

    cy.get('[href="/quiz/management"] > .q-item__section--side > .q-icon').click();
    cy.get(':nth-child(1) > [style="min-width: 250px; white-space: normal;"]').should(
      'contain',
      'TC-03-02-01 create quiz',
    );
  });

  it('TC-03-02-02 create quiz no title', () => {
    cy.get('.q-table__container').should('be.visible');
    cy.get('.q-gutter-sm > .q-btn').click();
    cy.wait(2000);
    cy.get('[href="/quiz/management"] > .q-item__section--side > .q-icon').click();
    cy.get(':nth-child(1) > [style="min-width: 250px; white-space: normal;"]').should(
      'contain',
      'แบบฟอร์มไม่มีชื่อ',
    );
  });
});
