<template>
  <q-dialog v-model="showSuccessDialog">
    <q-card class="q-pa-md text-center" style="width: 350px; max-width: 90vw">
      <q-card-section>
        <div class="text-h6 text-weight-bold">ทำแบบสอบถามเสร็จสิ้น</div>
      </q-card-section>

      <q-card-section class="q-pt-none">
        <q-icon name="check_circle" color="positive" size="100px" class="q-mb-md" />
      </q-card-section>

      <q-card-actions align="center" class="q-gutter-md">
        <q-btn flat label="ยกเลิก" color="grey-7" @click="showSuccessDialog = false" />
        <q-btn
          unelevated
          label="ยืนยัน"
          color="amber-6"
          text-color="white"
          @click="confirmAction"
        />
      </q-card-actions>
    </q-card>
  </q-dialog>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { useQuasar } from 'quasar';
import { summaryService } from 'src/services/asm/submissionService';
import router from 'src/router';
const $q = useQuasar();

const props = defineProps<{ modelValue: boolean; submitId: number }>();
const emit = defineEmits(['update:modelValue']);

const showSuccessDialog = computed({
  get: () => props.modelValue,
  set: (val: boolean) => emit('update:modelValue', val),
});

const confirmAction = async () => {
  // Logic for when the "Confirm" button is clicked
  console.log('Confirm action performed!');
  showSuccessDialog.value = false; // Close the dialog
  $q.notify({
    message: 'ยืนยันสำเร็จ!',
    color: 'positive',
    icon: 'check_circle',
  });
  await summaryService.update(props.submitId);
  await router.push({
    name: 'evaluate-management', // ✅ ชื่อตรงกับที่ตั้งไว้
  });
};
</script>
