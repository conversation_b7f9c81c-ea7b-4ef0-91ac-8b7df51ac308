import {
  BadRequestException,
  ForbiddenException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import type { UpdateSubmissionDto } from '../dto/updates/update-submission.dto';
import type { CreateSubmissionDto } from '../dto/creates/create-submission.dto';
import { StartQuizDto } from '../dto/start-quiz.dto';
import { Submission } from '../entities/submission.entity';
import { InjectEntityManager, InjectRepository } from '@nestjs/typeorm';
import { EntityManager, Repository, IsNull } from 'typeorm';
import { Assessment } from '../entities/assessment.entity';
import { QuizHelperService } from '../services/quiz-helper.service';

@Injectable()
export class SubmissionsService {
  constructor(private quizHelperService: QuizHelperService) {}

  @InjectEntityManager()
  private entityManager: EntityManager;
  @InjectRepository(Submission)
  private submissiobRepository: Repository<Submission>;

  async create(createSubmissionDto: CreateSubmissionDto) {
    const submission = this.submissiobRepository.create({
      assessmentId: createSubmissionDto.assessmentId,
      userId: createSubmissionDto.userId,
      startAt: new Date(),
      endAt: null,
    });

    return await this.submissiobRepository.save(submission); // ← ต้อง save
  }

  findAll() {
    return `This action returns all submissions`;
  }

  findOne(id: number) {
    return this.submissiobRepository.findOne({
      relations: ['responses'],
    });
  }

  findDraftByAsmIdAndUserId(asmId: number, userId: number) {
    return this.submissiobRepository.findOne({
      where: {
        assessmentId: asmId,
        userId: userId,
        submitAt: IsNull(),
        endAt: IsNull(),
      },
      relations: ['responses'],
    });
  }

  update(id: number, updateSubmissionDto: UpdateSubmissionDto) {
    return `This action updates a #${id} submission`;
  }

  remove(id: number) {
    return `This action removes a #${id} submission`;
  }

  async startAssessment(startQuizDto: StartQuizDto) {
    const { linkUrl, userId } = startQuizDto;
    const now = new Date();

    // Step 1: Find assessment
    let assessment: Assessment;
    try {
      assessment =
        await this.quizHelperService.findAssessmentByLinkUrl(linkUrl);
    } catch (error) {
      throw error;
    }

    // Step 2: Check if assessment is still open
    if (assessment.endAt && assessment.endAt < now) {
      throw new BadRequestException(
        `Assessment has ended on ${assessment.endAt.toLocaleString()}. No new submissions are allowed.`,
      );
    }

    // Step 3: Check for existing active submission
    const activeSubmission = await this.entityManager.findOne(Submission, {
      where: {
        assessmentId: assessment.id,
        userId,
        submitAt: IsNull(),
      },
    });

    // Step 4: Handle active submission
    if (activeSubmission) {
      // If submission timed out but not submitted, mark as submitted and create new
      if (activeSubmission.endAt < now && !activeSubmission.submitAt) {
        activeSubmission.submitAt = now;
        await this.entityManager.save(Submission, activeSubmission);
        // Continue to create new submission below
      } else if (activeSubmission.endAt >= now) {
        // If not timed out, return the active submission
        return activeSubmission;
      } else if (activeSubmission.submitAt) {
        // Already submitted, continue to create new
      }
    }

    // Step 5: Check submission limit
    try {
      await this.quizHelperService.validateUserCanAttempt(
        assessment,
        userId,
        this.entityManager,
      );
    } catch (error) {
      throw new ForbiddenException(error.message);
    }

    // Step 6: Create new submission
    const endAt =
      assessment.timeout > 0
        ? new Date(now.getTime() + assessment.timeout * 1000)
        : null;
    const submission = this.entityManager.create(Submission, {
      assessmentId: assessment.id,
      userId,
      startAt: now,
      endAt,
    });
    const savedSubmission = await this.entityManager.save(
      Submission,
      submission,
    );
    return savedSubmission;
  }

  async submitAssessment(submissionId: number) {
    const submission = await this.entityManager.findOne(Submission, {
      where: { id: submissionId },
    });
    if (!submission) {
      throw new NotFoundException('Submission not found');
    }

    // validate submission is not submitted yet
    if (submission.submitAt) {
      throw new BadRequestException('Submission already submitted');
    }
    submission.endAt = new Date();
    submission.submitAt = new Date();
    return this.entityManager.save(Submission, submission);
  }

  async getQuizScore(submissionId: number) {
    
    const submission = await this.entityManager.findOne(Submission, {
      where: { id: submissionId },
      relations: {
        responses: { 
          question: {
            itemBlock: true,
          }, 
          selectedOption: true 
        },
      },
      select: {
        id: true,
        startAt: true,
        endAt: true,
        submitAt: true,
        responses: {
          id: true,
          question: {
            id: true,
            score: true,
            itemBlock: {
              id: true,
              type: true,
            },
          },
          selectedOption: {
            id: true,
            value: true,
          },
        },
      },
    });

    if (!submission) {
      throw new NotFoundException('Submission not found');
    }

    console.log('Submission with relations:', JSON.stringify(submission, null, 2));


    // Calculate score using helper service
    const scoreResult = await this.quizHelperService.calculateScore(
      submission,
      submission.assessment,
    );

  
    // Calculate used time using helper service
    const usedTimeSeconds = this.quizHelperService.calculateTimeSpent(
      submission.startAt,
      submission.endAt,
    );
    const usedTimeFormatted = this.quizHelperService.formatTime(
      usedTimeSeconds,
      { format: 'hms' },
    );

    return {
      score: scoreResult.score,
      totalScore: scoreResult.totalScore,
      usedTimeFormatted, // in HH:MM:SS format
      submitTime: submission.endAt,
    };
  }
}
