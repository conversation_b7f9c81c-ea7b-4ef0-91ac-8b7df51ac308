// file management controller file

import { <PERSON>, Param, Get } from '@nestjs/common';
import { FileUploadService } from './file-upload.service';
import { ApiTags } from '@nestjs/swagger';

@ApiTags('File Upload')
@Controller('file-upload')
export class FileUploadController {
  constructor(private readonly fileUploadService: FileUploadService) {}

  // get public file
  @Get('get-public-file/:imagePath')
  async getPublicFile(@Param('imagePath') imagePath: string) {
    return this.fileUploadService.processImagePath(imagePath);
  }

}
