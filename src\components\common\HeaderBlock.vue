<template>
  <div class="header-block q-pa-md">
    <q-input v-model="headerText" label="Header Text" outlined @blur="handleBlur" />
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';

const emit = defineEmits<{
  (e: 'blur', fieldType: string, content: string): void;
}>();

const headerText = ref('');

function handleBlur() {
  emit('blur', 'header', headerText.value);
}
</script>

<style scoped>
.header-block {
  background-color: #f5f5f5;
  border-radius: 8px;
  border: 1px solid #e0e0e0;
}
</style>
