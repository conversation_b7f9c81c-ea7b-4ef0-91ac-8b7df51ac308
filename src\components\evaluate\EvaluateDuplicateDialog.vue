<template>
  <q-dialog v-model="blockCreatorStore.duplicateDialog" persistent>
    <q-card
      class="q-pa-lg q-mx-md"
      style="border-radius: 16px; min-width: 700px; max-width: 1000px"
    >
      <q-card-section>
        <q-input v-model="search" placeholder="ค้นหา" outlined dense class="full-width">
          <template v-slot:prepend>
            <q-icon name="search" />
          </template>
        </q-input>
      </q-card-section>

      <q-separator />

      <q-card-section style="max-height: 400px" class="scroll">
        <div class="row q-col-gutter-md">
          <div
            v-for="(item, index) in filteredFormData"
            :key="index"
            class="col-12 col-sm-6 col-md-4"
          >
            <q-btn
              :label="item.name?.length > 25 ? item.name.slice(0, 25) + '...' : item.name"
              unelevated
              outline
              class="full-width"
              @click="toggleSelection(item.id)"
              :color="selected === item.id ? 'primary' : 'grey-7'"
            />
          </div>
        </div>
      </q-card-section>
      <q-card-section class="q-pa-md q-gutter-sm flex justify-center items-center">
        <q-btn icon="arrow_back" flat :disable="!hasPrev" @click="goPrev" />
        <q-pagination
          v-model="pagination.page!"
          :max="totalPages"
          max-pages="7"
          @update:model-value="fetchData"
          boundary-numbers
        />
        <q-btn icon-right="arrow_forward" flat :disable="!hasNext" @click="goNext" />
      </q-card-section>

      <!-- <q-card-section class="q-pa-md q-gutter-sm flex justify-center">
        <q-pagination
          v-model="pagination.page!"
          :max="totalPages"
          max-pages="7"
          @update:model-value="fetchData"
          boundary-numbers
        />
      </q-card-section> -->

      <q-card-actions align="right">
        <q-btn label="ยกเลิก" color="grey-4" class="text-black" @click="cancel" />
        <q-btn label="ยืนยัน" color="primary" class="text-black" @click="confirm" />
        <!-- <q-btn label="ยกเลิก" flat color="warning" @click="cancel" />
        <q-btn label="ยืนยัน" color="primary" @click="confirm" /> -->
      </q-card-actions>
    </q-card>
  </q-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { AssessmentService } from 'src/services/asm/assessmentService';
import type { Assessment } from 'src/types/models';
import { useBlockCreatorStore } from 'src/stores/block_creator';
import type { QTableProps } from 'quasar';

const emit = defineEmits<{
  (e: 'update:modelValue', value: boolean): void;
  (e: 'confirm', value: number): void;
}>();
const defaultPaginationValue: QTableProps['pagination'] = {
  sortBy: 'id',
  page: 1,
  rowsPerPage: 9,
  descending: false, //ascending
  rowsNumber: 0,
};
const hasNext = ref(false);
const hasPrev = ref(false);
const totalPages = ref(1);
const search = ref('');
const selected = ref<number | null>(null);
const pagination = ref({ ...defaultPaginationValue });
const formData = ref<Assessment[]>([]);
const blockCreatorStore = useBlockCreatorStore();
const filteredFormData = computed(() =>
  formData.value.filter((item) => item.name?.toLowerCase().includes(search.value.toLowerCase())),
);

const cancel = () => emit('update:modelValue', false);

const confirm = () => {
  if (selected.value) {
    emit('confirm', selected.value);
    emit('update:modelValue', false);
  }
};
const goNext = async () => {
  if (hasNext.value) {
    pagination.value.page!++;
    await fetchData();
  }
};

const goPrev = async () => {
  if (hasPrev.value && pagination.value.page! > 1) {
    pagination.value.page!--;
    await fetchData();
  }
};
const toggleSelection = (itemId: number) => {
  selected.value = selected.value === itemId ? null : itemId;
};
const fetchData = async () => {
  try {
    const res = await new AssessmentService('evaluate').fetchAll(pagination.value, search.value);
    formData.value = res.data;
    totalPages.value = Math.ceil(res.total / pagination.value.rowsPerPage!);
    pagination.value.page = res.curPage;
    hasNext.value = res.hasNext;
    hasPrev.value = res.hasPrev;
  } catch (err) {
    console.error(err);
  }
};
watch(search, async () => {
  pagination.value.page = 1;
  pagination.value.rowsPerPage = 9;
  await fetchData();
});
watch(
  () => blockCreatorStore.duplicateDialog,
  async (val) => {
    if (val) {
      await fetchData();
    }
  },
  { immediate: false },
);
</script>
