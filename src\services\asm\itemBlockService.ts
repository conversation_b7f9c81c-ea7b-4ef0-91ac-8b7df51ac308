import { api } from 'src/boot/axios';
import { Notify } from 'quasar';
import type { ItemBlock } from 'src/types/models';

const showError = (message: string) => {
  Notify.create({
    message,
    type: 'negative',
    position: 'bottom',
    timeout: 3000,
  });
};

export class ItemBlockService {
  private path = 'item-blocks';

  async getAllWhereAsmId(assessmentId: number): Promise<ItemBlock[]> {
    try {
      const response = await api.get<ItemBlock[]>(`${this.path}`, {
        params: { assessmentId },
      });
      return response.data;
    } catch {
      showError('ไม่สามารถดึงข้อมูล ItemBlock ได้');
      throw new Error('Fetch item blocks failed');
    }
  }

  async addItemBlock(params: ItemBlock): Promise<ItemBlock> {
    try {
      const response = await api.post<ItemBlock>(`${this.path}`, { params });
      return response.data;
    } catch {
      showError('ไม่สามารถเพิ่ม ItemBlock ได้');
      throw new Error('Add item block failed');
    }
  }

  async updateItemBlockField(itemId: number, params: ItemBlock): Promise<ItemBlock> {
    try {
      const response = await api.patch<ItemBlock>(`${this.path}/${itemId}`, { params });
      return response.data;
    } catch {
      showError('ไม่สามารถอัปเดต ItemBlock ได้');
      throw new Error('Update item block field failed');
    }
  }

  async removeItemBlock(itemId: number): Promise<void> {
    try {
      await api.delete(`${this.path}/${itemId}`);
    } catch {
      showError('ไม่สามารถลบ ItemBlock ได้');
      throw new Error('Remove item block failed');
    }
  }

  async getOne(itemBlockId: number): Promise<ItemBlock> {
    try {
      const response = await api.get<ItemBlock>(`${this.path}/${itemBlockId}`);
      return response.data;
    } catch {
      showError('ไม่สามารถโหลด ItemBlock ได้');
      throw new Error('Failed to fetch ItemBlock');
    }
  }

  async updateDimensions(
    itemBlockId: number,
    dimensions: { width: number; height: number },
  ): Promise<ItemBlock> {
    try {
      const response = await api.patch(`${this.path}/${itemBlockId}/dimensions`, dimensions);
      return response.data;
    } catch {
      showError('ไม่สามารถอัปเดตขนาดรูปภาพได้');
      throw new Error('Failed to update image dimensions');
    }
  }
}
