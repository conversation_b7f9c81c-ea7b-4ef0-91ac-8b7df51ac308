import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  ParseIntPipe,
  HttpException,
  HttpStatus,
  UseGuards,
} from '@nestjs/common';
import { Response } from '../entities/response.entity';

import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBody,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { ResponsesService } from './responses.service';

import { CreateResponseDto } from '../dto/creates/create-response.dto';
import type { UpdateResponseDto } from '../dto/updates/update-response.dto';
import { AuthGuard } from 'src/auth/auth.guard';

@ApiTags('ASM - Responses')
@ApiBearerAuth()
@UseGuards(AuthGuard)
@Controller('responses')
export class ResponsesController {
  constructor(private readonly responsesService: ResponsesService) {}

  @Post()
  @ApiOperation({ summary: 'สร้างคำตอบใหม่' })
  @ApiResponse({
    status: 201,
    description: 'สร้างคำตอบสำเร็จ',
    schema: {
      example: {
        submissionId: 1,
        questionId: 1,
        selectedOptionId: 5,
        answerText: null,
      },
    },
  })
  async create(@Body() createResponseDto: CreateResponseDto) {
    try {
      return await this.responsesService.create(createResponseDto);
    } catch (error) {
      throw new HttpException(error.message, HttpStatus.BAD_REQUEST);
    }
  }

  @Get()
  @ApiOperation({ summary: 'ดึงข้อมูลคำตอบทั้งหมด' })
  @ApiResponse({
    status: 200,
    description: 'ดึงข้อมูลสำเร็จ',
  })
  async findAll() {
    try {
      return await this.responsesService.findAll();
    } catch (error) {
      throw new HttpException(error.message, HttpStatus.BAD_REQUEST);
    }
  }

  @Get(':id')
  @ApiOperation({ summary: 'ดึงข้อมูลคำตอบตาม ID' })
  @ApiResponse({
    status: 200,
    description: 'ดึงข้อมูลสำเร็จ',
  })
  @ApiResponse({
    status: 404,
    description: 'ไม่พบข้อมูลคำตอบ',
  })
  async findOne(@Param('id', ParseIntPipe) id: number) {
    try {
      const response = await this.responsesService.findOne(id);
      if (!response) {
        throw new HttpException('Response not found', HttpStatus.NOT_FOUND);
      }
      return response;
    } catch (error) {
      throw new HttpException(error.message, HttpStatus.BAD_REQUEST);
    }
  }

  @Get('/:submissionId/:questionId')
  @ApiOperation({ summary: 'ดึงข้อมูลคำตอบตาม ID' })
  @ApiResponse({
    status: 200,
    description: 'ดึงข้อมูลสำเร็จ',
  })
  @ApiResponse({
    status: 404,
    description: 'ไม่พบข้อมูลคำตอบ',
  })
  async findAnswer(
    @Param('submissionId', ParseIntPipe) id: number,
    @Param('questionId', ParseIntPipe) questionId: number,
  ) {
    try {
      const response = await this.responsesService.findEvaluateAnswer(
        id,
        questionId,
      );
      if (!response) {
        throw new HttpException('Response not found', HttpStatus.NOT_FOUND);
      }
      return response;
    } catch (error) {
      throw new HttpException(error.message, HttpStatus.BAD_REQUEST);
    }
  }

  @Get('/checkbox/:submissionId/:questionId')
  async findCheckboxAnswers(
    @Param('submissionId') submissionId: number,
    @Param('questionId') questionId: number,
  ): Promise<Response[]> {
    try {
      const responses = await this.responsesService.findEvaluateAnswers(
        submissionId,
        questionId,
      );
      return responses; // จะ return [] หากไม่เจอข้อมูล
    } catch (error) {
      throw new HttpException(
        error instanceof Error
          ? error.message
          : 'Failed to fetch checkbox answers',
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  @Get('/:submissionId/:questionId/:selectedOptionId')
  @ApiOperation({ summary: 'ดึงข้อมูลคำตอบตาม ID' })
  @ApiResponse({
    status: 200,
    description: 'ดึงข้อมูลสำเร็จ',
  })
  @ApiResponse({
    status: 404,
    description: 'ไม่พบข้อมูลคำตอบ',
  })
  async findRemoveCheckBoxAnswer(
    @Param('submissionId', ParseIntPipe) id: number,
    @Param('questionId', ParseIntPipe) questionId: number,
    @Param('selectedOptionId', ParseIntPipe) selectedOptionId: number,
  ) {
    try {
      const response = await this.responsesService.findEvaluateCheckBoxAnswer(
        id,
        questionId,
        selectedOptionId,
      );
      if (!response) {
        throw new HttpException('Response not found', HttpStatus.NOT_FOUND);
      }
      return response;
    } catch (error) {
      throw new HttpException(error.message, HttpStatus.BAD_REQUEST);
    }
  }

  @Patch(':id')
  @ApiOperation({ summary: 'แก้ไขคำตอบ' })
  @ApiResponse({
    status: 200,
    description: 'แก้ไขคำตอบสำเร็จ',
    schema: {
      example: {
        selectedOptionId: 6,
        answerText: 'Updated answer',
      },
    },
  })
  async update(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateResponseDto: UpdateResponseDto,
  ) {
    try {
      return await this.responsesService.update(id, updateResponseDto);
    } catch (error) {
      throw new HttpException(error.message, HttpStatus.BAD_REQUEST);
    }
  }

  @Delete(':id')
  @ApiOperation({ summary: 'ลบคำตอบ' })
  @ApiResponse({
    status: 200,
    description: 'ลบคำตอบสำเร็จ',
  })
  async remove(@Param('id', ParseIntPipe) id: number) {
    try {
      await this.responsesService.remove(id);
    } catch (error) {
      throw new HttpException(error.message, HttpStatus.BAD_REQUEST);
    }
  }

  @Post('quiz/save-response')
  @ApiBody({ type: CreateResponseDto })
  async userSaveQuizResponse(@Body() createResponseDto: CreateResponseDto) {
    console.log('Received DTO:', createResponseDto); // 👈 log

    return await this.responsesService.userSaveQuizResponse(createResponseDto);
  }

  @Get('by/submission/:submissionId')
  @ApiOperation({ summary: ' ข้อมูลคำตอบทั้งหมดของ submission ตาม ID' })
  async findAllBySubmissionId(@Param('submissionId') submissionId: string) {
    try {
      return await this.responsesService.findAllBySubmissionId(
        Number(submissionId),
      );
    } catch (error) {
      throw new HttpException(error.message, HttpStatus.BAD_REQUEST);
    }
  }
}
