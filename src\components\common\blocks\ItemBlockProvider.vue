<script setup lang="ts">
import { provide, watch } from 'vue';
import { createItemBlockStore } from 'src/stores/item_block_store';
import type { ItemBlock } from 'src/types/models';

const props = defineProps<{
  blockId: number;
  itemBlock?: ItemBlock;
}>();

// Create the store for this block, passing the itemBlock data
const blockStore = createItemBlockStore(props.blockId, props.itemBlock);
const storeInstance = blockStore();

// Watch for changes in itemBlock and update store from backend response
watch(
  () => props.itemBlock,
  (newItemBlock, oldItemBlock) => {
    if (!newItemBlock || !oldItemBlock) return;

    // Check if the type changed or options array changed
    const typeChanged = newItemBlock.type !== oldItemBlock.type;
    const optionsChanged =
      JSON.stringify(newItemBlock.options) !== JSON.stringify(oldItemBlock.options);

    // Check if questions array changed (for GRID and TEXTFIELD types)
    const questionsChanged =
      JSON.stringify(newItemBlock.questions) !== JSON.stringify(oldItemBlock.questions);

    // Check if only isRequired changed (should NOT trigger reinitialization)
    const onlyIsRequiredChanged =
      !typeChanged &&
      !optionsChanged &&
      !questionsChanged &&
      newItemBlock.isRequired !== oldItemBlock.isRequired;

    // Only update store if there are structural changes, not just isRequired changes
    if ((typeChanged || optionsChanged || questionsChanged) && !onlyIsRequiredChanged) {
      console.log(
        '🔄 [ItemBlockProvider] Detected structural itemBlock changes, updating store from backend:',
        {
          blockId: props.blockId,
          typeChanged,
          optionsChanged,
          questionsChanged,
          oldType: oldItemBlock.type,
          newType: newItemBlock.type,
          oldOptionsCount: oldItemBlock.options?.length || 0,
          newOptionsCount: newItemBlock.options?.length || 0,
          oldQuestionsCount: oldItemBlock.questions?.length || 0,
          newQuestionsCount: newItemBlock.questions?.length || 0,
        },
      );

      // Update store from backend response (do not reset manually)
      if (newItemBlock.type === 'RADIO') {
        storeInstance.radioOptions = (newItemBlock.options || []).map((opt, idx) => ({
          id: opt.id,
          placeholder: opt.optionText || `ตัวเลือกที่ ${opt.sequence}`,
          value: String(opt.value || `option${opt.sequence}`),
          optionText: opt.optionText || '',
          score: opt.value || 0,
          sequence: opt.sequence || idx + 1,
        }));
      } else if (newItemBlock.type === 'CHECKBOX') {
        storeInstance.checkboxOptions = (newItemBlock.options || []).map((opt, idx) => ({
          id: opt.id,
          placeholder: opt.optionText || `ตัวเลือกที่ ${opt.sequence}`,
          value: String(opt.value || `option${opt.sequence}`),
          optionText: opt.optionText || '',
          score: opt.value || 0,
          sequence: opt.sequence || idx + 1,
        }));
      } else if (newItemBlock.type === 'GRID') {
        storeInstance.gridColumnOptions = (newItemBlock.options || []).map((opt, idx) => ({
          id: opt.id,
          label: opt.optionText || `ตัวเลือกที่ ${opt.sequence}`,
          value: `option${idx + 1}`,
          optionText: opt.optionText || '',
          score: opt.value || 0,
          sequence: opt.sequence || idx + 1,
        }));
        storeInstance.gridRowQuestions = (newItemBlock.questions || [])
          .filter((q) => !q.isHeader)
          .map((q, idx) => ({
            id: q.id,
            label: q.questionText || '',
            value: `question${idx + 1}`,
            sequence: q.sequence || idx + 1,
          }));
      } else if (newItemBlock.type === 'TEXTFIELD') {
        storeInstance.textInput =
          (newItemBlock.questions && newItemBlock.questions[0]?.questionText) || '';
      }
    } else if (onlyIsRequiredChanged) {
      console.log(
        '🔄 [ItemBlockProvider] Only isRequired changed, skipping store reinitialization:',
        {
          blockId: props.blockId,
          oldIsRequired: oldItemBlock.isRequired,
          newIsRequired: newItemBlock.isRequired,
        },
      );
    }
  },
  { deep: true },
);

// Provide the store to child components
provide('blockStore', storeInstance);
</script>

<template>
  <slot />
</template>
