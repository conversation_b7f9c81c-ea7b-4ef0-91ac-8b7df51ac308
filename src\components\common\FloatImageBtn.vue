<script setup lang="ts">
import { ref, watch, computed } from 'vue';
import { useImageStore } from 'stores/image';
import { useGlobalStore } from 'src/stores/global';
import { ImageBodyService } from 'src/services/asm/imageBodyService';
import type { ItemBlock, ImageBody } from 'src/types/models';

const props = defineProps<{
  itemBlock?: ItemBlock;
  // Alternative props for direct dimension control (used by ItemBlockComponent)
  originalWidth?: number;
  originalHeight?: number;
  currentWidth?: number;
  currentHeight?: number;
}>();

// Utility function to detect if a path is a relative path (not a signed URL)
const isRelativePath = (path: string | null | undefined): boolean => {
  if (!path) return false;
  // Relative paths start with 'uploaded_files/' and don't contain query parameters or full URLs
  return path.startsWith('uploaded_files/') && !path.includes('?') && !path.includes('http');
};

// Utility function to extract relative path from signed URL
const extractRelativePath = (path: string | null | undefined): string | null => {
  if (!path) return null;

  // If it's already a relative path, return as is
  if (isRelativePath(path)) return path;

  // If it's a signed URL, extract the relative path part
  if (path.includes('uploaded_files/')) {
    const match = path.match(/uploaded_files\/[^?]+/);
    return match ? match[0] : null;
  }

  return path;
};

const emit = defineEmits<{
  (e: 'dimensions-updated', dimensions: { width: number; height: number }): void;
  (e: 'resize', dimensions: { width: number; height: number }): void;
}>();

const showPanel = ref(false);
const imageStore = useImageStore();
const globalStore = useGlobalStore();
const menuStyle = ref({
  left: '100%',
  top: '0px',
});

// Get initial dimensions from multiple sources with priority order
const initialWidth = computed(() => {
  // 1. Direct props (used by ItemBlockComponent)
  if (props.currentWidth && props.currentWidth > 0) {
    return props.currentWidth;
  }
  // 2. ItemBlock imageBody (used by ImageBlock)
  if (props.itemBlock?.imageBody?.imageWidth && props.itemBlock.imageBody.imageWidth > 0) {
    return props.itemBlock.imageBody.imageWidth;
  }
  // 3. ItemBlock question (used by ItemBlockComponent)
  if (props.itemBlock?.questions?.[0]?.imageWidth && props.itemBlock.questions[0].imageWidth > 0) {
    return props.itemBlock.questions[0].imageWidth;
  }
  // 4. Fallback to imageStore
  return imageStore.widthPixel || 0;
});

const initialHeight = computed(() => {
  // 1. Direct props (used by ItemBlockComponent)
  if (props.currentHeight && props.currentHeight > 0) {
    return props.currentHeight;
  }
  // 2. ItemBlock imageBody (used by ImageBlock)
  if (props.itemBlock?.imageBody?.imageHeight && props.itemBlock.imageBody.imageHeight > 0) {
    return props.itemBlock.imageBody.imageHeight;
  }
  // 3. ItemBlock question (used by ItemBlockComponent)
  if (
    props.itemBlock?.questions?.[0]?.imageHeight &&
    props.itemBlock.questions[0].imageHeight > 0
  ) {
    return props.itemBlock.questions[0].imageHeight;
  }
  // 4. Fallback to imageStore
  return imageStore.heightPixel || 0;
});

// Local reactive dimensions for the input fields
const localWidth = ref(initialWidth.value);
const localHeight = ref(initialHeight.value);

// Temporary values for cancel functionality
const tempWidth = ref(localWidth.value);
const tempHeight = ref(localHeight.value);

// Watch for changes in itemBlock dimensions to update local state
watch(
  () => props.itemBlock?.imageBody?.imageWidth,
  (newWidth) => {
    if (newWidth && newWidth > 0) {
      localWidth.value = newWidth;
      tempWidth.value = newWidth;
    }
  },
  { immediate: true },
);

watch(
  () => props.itemBlock?.imageBody?.imageHeight,
  (newHeight) => {
    if (newHeight && newHeight > 0) {
      localHeight.value = newHeight;
      tempHeight.value = newHeight;
    }
  },
  { immediate: true },
);

// Watch for changes in question dimensions (for ItemBlockComponent)
watch(
  () => props.itemBlock?.questions?.[0]?.imageWidth,
  (newWidth) => {
    if (newWidth && newWidth > 0) {
      localWidth.value = newWidth;
      tempWidth.value = newWidth;
    }
  },
  { immediate: true },
);

watch(
  () => props.itemBlock?.questions?.[0]?.imageHeight,
  (newHeight) => {
    if (newHeight && newHeight > 0) {
      localHeight.value = newHeight;
      tempHeight.value = newHeight;
    }
  },
  { immediate: true },
);

// Watch for changes in direct props (for ItemBlockComponent)
watch(
  () => props.currentWidth,
  (newWidth) => {
    if (newWidth && newWidth > 0) {
      localWidth.value = newWidth;
      tempWidth.value = newWidth;
    }
  },
  { immediate: true },
);

watch(
  () => props.currentHeight,
  (newHeight) => {
    if (newHeight && newHeight > 0) {
      localHeight.value = newHeight;
      tempHeight.value = newHeight;
    }
  },
  { immediate: true },
);

// Update temp values when panel opens
watch(showPanel, (isOpen) => {
  if (isOpen) {
    tempWidth.value = localWidth.value;
    tempHeight.value = localHeight.value;
  }
});

async function applySize() {
  try {
    // Validate dimensions
    const width = Number(tempWidth.value) || 0;
    const height = Number(tempHeight.value) || 0;

    if (width < 0 || height < 0) {
      console.warn('⚠️ Invalid dimensions provided, using absolute values');
    }

    const finalWidth = Math.abs(width);
    const finalHeight = Math.abs(height);

    console.log('🎯 Applying new image dimensions:', {
      width: finalWidth,
      height: finalHeight,
      itemBlockId: props.itemBlock?.id,
      imageBodyId: props.itemBlock?.imageBody?.id,
    });

    // Update local state immediately for responsive UI
    localWidth.value = finalWidth;
    localHeight.value = finalHeight;

    // Update global image store for backward compatibility
    imageStore.widthPixel = finalWidth;
    imageStore.heightPixel = finalHeight;

    // If we have an itemBlock with imageBody, update the backend (ImageBlock scenario)
    if (props.itemBlock?.imageBody?.id) {
      await updateBackendDimensions(finalWidth, finalHeight);
    }

    // Emit the updated dimensions to parent component
    emit('dimensions-updated', { width: finalWidth, height: finalHeight });
    // Also emit resize event for ItemBlockComponent compatibility
    emit('resize', { width: finalWidth, height: finalHeight });

    showPanel.value = false;
  } catch (error) {
    console.error('❌ Failed to apply image dimensions:', error);
  }
}

async function updateBackendDimensions(width: number, height: number) {
  try {
    globalStore.startSaveOperation('Updating image size...');

    const imageBodyService = new ImageBodyService();
    const imageBodyId = props.itemBlock!.imageBody!.id;
    const existingImageBody = props.itemBlock!.imageBody!;

    console.log('🔄 Updating image dimensions while preserving existing data:', {
      imageBodyId,
      newDimensions: { width, height },
      existingImageText: existingImageBody.imageText,
      existingImagePath: existingImageBody.imagePath,
      imagePathType: typeof existingImageBody.imagePath,
      imagePathLength: existingImageBody.imagePath?.length,
      imagePathTrimmed: existingImageBody.imagePath?.trim(),
    });

    // CRITICAL: Include existing imageText and imagePath to prevent them from being lost
    // The backend may reset these values if they're not provided in the update request
    const updatePayload: Partial<ImageBody> = {
      itemBlockId: props.itemBlock!.id,
      imageWidth: width,
      imageHeight: height,
    };

    // CRITICAL: Always include imageText to prevent backend from setting it to null
    // Send the actual existing value or undefined (don't send empty string)
    if (existingImageBody.imageText !== null && existingImageBody.imageText !== undefined) {
      updatePayload.imageText = existingImageBody.imageText;
    }

    // CRITICAL: Always send imagePath to prevent backend from setting it to null
    // The backend ALWAYS sets imageBody.imagePath = newImagePath, so we must send the current value
    // If we don't send it, it becomes undefined -> null and overwrites the existing path
    // CRITICAL: Use original relative path format to prevent signed URL conversion
    const originalPath = extractRelativePath(existingImageBody.imagePath);
    const pathToUse = originalPath || existingImageBody.imagePath;
    updatePayload.imagePath = pathToUse as string;
    console.log('🔒 Always including imagePath in payload (preserving relative format):', {
      originalPath: existingImageBody.imagePath,
      extractedRelativePath: originalPath,
      pathToUse: pathToUse,
      pathType: typeof pathToUse,
      willSendPath: updatePayload.imagePath,
    });

    console.log('🔒 Preserving existing image data in update payload (relative path format):', {
      originalImageText: existingImageBody.imageText,
      originalImagePath: existingImageBody.imagePath,
      extractedRelativePath: originalPath,
      originalImagePathType: typeof existingImageBody.imagePath,
      payloadImageText: updatePayload.imageText,
      payloadImagePath: updatePayload.imagePath,
      payloadImagePathType: typeof updatePayload.imagePath,
      willPreserveImage: !!updatePayload.imagePath,
      isRelativeFormat: isRelativePath(updatePayload.imagePath),
      payloadKeys: Object.keys(updatePayload),
    });

    // CRITICAL: If imagePath is null/undefined, we have a problem - log it
    if (!existingImageBody.imagePath) {
      console.error('❌ CRITICAL: existingImageBody.imagePath is null/undefined!', {
        existingImageBody,
        itemBlockId: props.itemBlock!.id,
        imageBodyId: props.itemBlock!.imageBody!.id,
      });
    }

    // CRITICAL: If we couldn't extract a relative path and have no path to use, log it
    if (!pathToUse) {
      console.error('❌ CRITICAL: No valid path to use for update!', {
        existingImagePath: existingImageBody.imagePath,
        extractedRelativePath: originalPath,
        pathToUse: pathToUse,
      });
    }

    await imageBodyService.updateImageBody(
      imageBodyId,
      updatePayload,
      // No file parameter - we're only updating dimensions
    );

    globalStore.completeSaveOperation(true, 'Image size updated successfully');

    console.log('✅ Backend dimensions updated successfully (relative path preserved):', {
      imageBodyId,
      width,
      height,
      preservedImageText: existingImageBody.imageText,
      originalImagePath: existingImageBody.imagePath,
      preservedRelativePath: pathToUse,
      isRelativeFormat: isRelativePath(pathToUse),
    });
  } catch (error) {
    globalStore.completeSaveOperation(false, 'Failed to update image size');
    console.error('❌ Failed to update backend dimensions:', error);
    throw error;
  }
}

function cancelSize() {
  // Reset temp values to current values
  tempWidth.value = localWidth.value;
  tempHeight.value = localHeight.value;
  showPanel.value = false;
}
</script>

<template>
  <div class="relative-position" style="background-color: transparent">
    <!-- ปุ่มจุดสามจุด -->
    <q-btn
      round
      flat
      fab
      icon="more_vert"
      @click="showPanel = !showPanel"
      padding="md"
      class="bg-grey-3"
      style="border-radius: 99px"
    />

    <!-- กล่องกรอกขนาด (แสดงใต้ปุ่ม) -->
    <q-slide-transition>
      <div v-show="showPanel" class="menu-box absolute" :style="menuStyle">
        <q-card class="q-pa-sm" style="width: 300px; z-index: 10">
          <q-card-section class="scroll" style="max-height: 40vh">
            <div class="row items-center">
              <span class="label" style="min-width: 80px; color: black">ความกว้าง</span>
              <q-input
                v-model.number="tempWidth"
                outlined
                dense
                class="pixel-input"
                type="number"
                min="0"
                :placeholder="localWidth.toString()"
              />
              <span class="label q-ml-md" style="color: black">พิกเซล</span>
            </div>

            <div class="row items-center q-mt-md">
              <span class="label" style="min-width: 80px; color: black">ความสูง</span>
              <q-input
                v-model.number="tempHeight"
                outlined
                dense
                class="pixel-input"
                type="number"
                min="0"
                :placeholder="localHeight.toString()"
              />
              <span class="label q-ml-md" style="color: black">พิกเซล</span>
            </div>

            <!-- Display current dimensions for reference -->
            <div class="row items-center q-mt-md" v-if="localWidth > 0 || localHeight > 0">
              <span class="text-caption text-grey-6">
                ขนาดปัจจุบัน: {{ localWidth }} × {{ localHeight }} พิกเซล
              </span>
            </div>
          </q-card-section>

          <q-card-actions align="left" class="row justify-between">
            <q-btn flat label="ยกเลิก" style="color: black" @click="cancelSize" />
            <q-btn flat label="ยืนยัน" color="primary" @click="applySize" />
          </q-card-actions>
        </q-card>
      </div>
    </q-slide-transition>
  </div>
</template>

<style scoped>
.pixel-input {
  max-width: 100px;
  font-size: 12px;
}
.menu-box {
  z-index: 1000;
}
</style>
